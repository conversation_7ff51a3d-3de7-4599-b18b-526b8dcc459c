<template>
    <div>
        <div class="app-container" v-if="showContainer">
            <!--        :permission="getPermission"-->
            <yo-table
                v-loading="tableLoading"
                :data="data"
                :option="option"
                ref="crud"
                :page.sync="page"
                :search.sync="search"
                @search-change="searchChange"
                @on-load="onLoad"
                @row-save="rowSave"
                @row-update="rowUpdate"
                v-model="formParent"
            >
                <template slot-scope="{ scope }" slot="searchMenu">
                    <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-search"
                        @click.stop="handleQuery"
                    >
                        查询
                    </el-button>
                    <el-button
                        size="mini"
                        icon="el-icon-refresh"
                        @click.stop="resetQuery"
                    >
                        重置
                    </el-button>
                </template>
                <!-- 自定义左侧操作栏 -->
                <template slot-scope="{ size }" slot="menuLeft">
                    <el-button
                        type="primary"
                        icon="el-icon-plus"
                        :size="size"
                        plain
                        @click.stop="handleAdd()"
                    >
                        新增
                    </el-button>
                    <el-button
                        type="primary"
                        icon="el-icon-download"
                        @click.stop="handleExp()"
                        :size="size"
                        plain
                    >
                        下载模板
                    </el-button>
                    <input
                        id="fileslist"
                        v-show="false"
                        type="file"
                        accept=".xls"
                        ref="fileRef"
                        @change="fileChange"
                    />
                    <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-upload2"
                        @click.stop="handleImport()"
                    >
                        导入
                    </el-button>
                </template>

                <!-- 自定义列 -->
                <template slot="state" slot-scope="scope">
                    <el-tag
                        size="mini"
                        effect="plain"
                        :type="tagType(scope.row)"
                    >
                        {{ status(scope.row) }}
                    </el-tag>
                </template>
                <!-- 自定义操作按钮 -->
                <template slot-scope="{ row, size, type, index }" slot="menu">
                    <el-button
                        :size="size"
                        :type="type"
                        icon="el-icon-edit"
                        @click.stop="handleUpdate(row, index)"
                    >
                        修改
                    </el-button>
                    <el-button
                        :size="size"
                        :type="type"
                        icon="el-icon-delete"
                        @click="handleDelete(row)"
                    >
                        删除
                    </el-button>
                </template>
            </yo-table>
        </div>
        <div v-else>
            <!-- <auth-user @show-detail="showDetails" :roleId="roleId"></auth-user> -->
            <auth-user
                v-if="isActive == 'authUser'"
                @show-detail="showDetails"
                :roleId="roleId"
                :roleobj="roleobj"
            ></auth-user>
            <auth-menu
                v-if="isActive == 'dataScope'"
                @show-detail="showDetails"
                :roleId="roleId"
            ></auth-menu>
        </div>
    </div>
</template>
<script>
import {
    listJxfpze,
    addJxfpze,
    updateJxfpze,
    delJxfpze,
    listSalarySub,
    downloadExample,
    importEx,
} from '@/api/jxkh/jxfpze';

import salarySubListOption from './infoData/JxfpzeListOption.js';

export default {
    name: 'Role',
    dicts: ['jxkh_salary_batch_type'],
    data() {
        return {
            roleId: '',
            showContainer: true, //显示主界面
            isActive: 'authUser', // 配置用户 or 操作菜单权限
            activeRoleName: '', //
            roleobj: {},
            formParent: {
                status: '1',
            },
            fullscreen: false,
            search: {},
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            data: [],
            option: salarySubListOption,

            // 遮罩层
            loading: true,
            tableLoading: true,
            // 角色表格数据
            roleList: [],
            // 弹出层标题
            title: '',
            // 是否显示弹出层（数据权限）
            openDataScope: false,
            menuData: [],
            // menuOption: menuOption,
            // 修改选中项
            resList: [],
            multiple: true,
            num: 0,
            // 表单参数
            form: {},
            projectList: [], //科目列表
        };
    },
    computed: {
        styleName() {
            if (!this.fullscreen) {
                return { top: '15%', bottom: '5%' };
            } else {
                return { top: 0, bottom: 0, marginTop: 0 };
            }
        },
    },
    created() {
        /** *添加字典项数据*/
        // this.updateDictData(
        //   this.option.column,
        //   "category",
        //   this.dict.type["jxkh_kmwh_category"]
        // );
        // this.updateDictData(
        //   this.option.column,
        //   "fcategory",
        //   this.dict.type["jxkh_kmwh_fcategory"]
        // );
        // this.updateDictData(
        //   this.option.column,
        //   "state",
        //   this.dict.type["jxkh_kmwh_state"]
        // );
        this.updateDictData(
            this.option.column,
            'type',
            //this.dict.type["jxkh_salary_batch_type"]
            this.dict.type['jxkh_salary_batch_type']
        );
        this.getSubjectNameList({ state: '1' });
        this.updateDictData(
            this.option.column,
            'subjectName',
            //this.dict.type["jxkh_salary_batch_type"]
            this.projectList
        );
    },
    methods: {
        getSubjectNameList(query) {
            this.tableLoading = true;
            const params = { ...this.page, ...query };
            listSalarySub(params).then(response => {
                if (response.code == 0) {
                    var kmList = response.data.list;
                    for (let obj of kmList) {
                        var item = { label: obj.name, value: obj.name };
                        console.log(item.lable + 'wsq');
                        console.log(item.value + 'wsq');
                        this.projectList.push(item);
                    }
                }
            });
        },

        showDetails() {
            this.showContainer = true;
        },
        //更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.$refs.crud.rowAdd();
        },
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
        },
        handleFullScreen() {
            this.fullscreen = !this.fullscreen;
        },
        // 超级管理员不能编辑和删除
        getPermission(key, row, index) {
            const flag =
                (key === 'editBtn' || key === 'delBtn') &&
                row.roleKey === 'admin';
            if (flag) {
                return false;
            } else {
                return true;
            }
        },
        // 新增表单保存
        async rowSave(form, done, loading) {
            debugger;
            let response = await addJxfpze(form);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.getList();
                done();
            }
        },
        // 修改表单保存
        async rowUpdate(form, index, done, loading) {
            let response = await updateJxfpze(form);
            if (response.code == 0) {
                this.$message.success(response.message);
                this.getList();
                done();
            }
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },

        /** 查询角色列表 */
        getList(query) {
            this.tableLoading = true;
            const params = { ...this.page, ...query };
            listJxfpze(params).then(response => {
                if (response.code == 0) {
                    this.data = response.data.list;
                    this.page.total = response.data.total;
                    this.tableLoading = false;
                }
            });
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            this.getList(this.search);
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
            // this.handleQuery();
        },
        // 更多操作触发
        handleCommand(command, row) {
            switch (command) {
                case 'handleDataScope':
                    this.handleMoreProcess(row, 'dataScope');
                    break;
                case 'handleAuthUser':
                    this.handleMoreProcess(row, 'authUser');
                    break;
                default:
                    break;
            }
        },
        /** 修改按钮操作 */
        handleUpdate(row, index) {
            this.formParent = row;
            this.$refs.crud.rowEdit(row, index);
        },
        /** 分配数据权限操作 or  分配用户操作*/
        handleMoreProcess(row, type) {
            this.roleId = row.roleId;
            this.roleobj = row;
            this.showContainer = false;
            this.isActive = type;
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const id = row.id;
            this.$confirm(`是否确认删除id为" ${id} "的数据项?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                // this.$modal
                //   .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
                .then(async () => {
                    // 删除
                    this.handleDelRole(id, delJxfpze);
                })
                .catch(() => {});
        },
        // 删除角色
        async handleDelRole(roleIds, delFn) {
            let res = await delFn(roleIds);
            if (res.code == 0) {
                this.$message.success(res.message);
                this.getList();
            }
        },
        handleExp() {
            downloadExample()
                .then(res => {
                    debugger;
                    this.handleExportData(res);
                })
                .catch(function (error) {
                    if (error.response) {
                        console.log(error.response);
                    } else if (error.request) {
                        console.log(error.request);
                    } else {
                        console.log(error.message);
                    }
                    console.log(error);
                });
        },
        /** 导出结果，处理返回的流文件 */
        handleExportData(res) {
            console.log('res:', res);
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        status(row) {
            switch (row.state) {
                case '1':
                    return '启用';
                case '2':
                    return '停用';
                case '3':
                    return '注销';
                case '4':
                    return '锁定';
                default:
                    break;
            }
        },
        /** 导入按钮操作 */
        handleImport() {
            if (this.search.type == null || this.search.type == '') {
                this.$modal.msgWarning('请选择下载的绩效类型！');
                return;
            } else {
                if (this.search.type == '1') {
                    //月度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearMonth == '') {
                        this.$modal.msgWarning('请选择下载的月度！');
                        return;
                    }
                }
                if (this.search.type == '2') {
                    //季度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearQuar == '') {
                        this.$modal.msgWarning('请选择下载的季度！');
                        return;
                    }
                }
                if (this.search.type == '3') {
                    //年度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                }
                let date = new Date(this.search.year);
                let year = date.getFullYear().toString();
                if (year == '1970') {
                    year = '';
                }
                this.search.year = year;
                let date2 = new Date(this.search.yearMonth);
                let year2 = date2.getFullYear();
                let month = ('0' + (date2.getMonth() + 1)).slice(-2);
                if (year2 == '1970' && month == '01') {
                    month = '';
                }
                if (month == 'aN') {
                    month = '';
                }
                this.search.yearMonth = month;
                this.$refs.fileRef.click();
            }
        },
        fileChange(event) {
            let file = document.querySelector('#fileslist').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('type', this.search.type);
            formData.append('year', this.search.year);
            formData.append('yearQuar', this.search.yearQuar);
            formData.append('yearMonth', this.search.yearMonth);
            importEx(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        //tag样式
        tagType(row) {
            switch (row.state) {
                case '1':
                    return 'success';
                case '2':
                    return 'danger';
                case '3':
                    return 'info';
                case '4':
                    return 'warning';
                default:
                    break;
            }
        },
        roleIdType(row) {
            switch (row.roleId) {
                case 'anonymous':
                    return 'danger';
                // return "success";
                case 'superAdmin':
                    return 'danger';
                case '3':
                    return 'info';
                case '4':
                    return 'warning';
                default:
                    break;
            }
        },
        tableRowClassName({ row, rowIndex }) {
            if (row.roleType === '1') {
                return 'superAdmin-row';
            } else if (row.roleType === '2') {
                return 'anonymous-row';
            } else {
                return '';
            }
        },
    },
};
</script>
<style scoped>
.yo-table__dialog__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.yo-table__dialog__menu {
    padding-right: 20px;
}
.item-badge {
    margin-top: 10px;
    margin-right: 10px;
}
/* 自定义行颜色 */
</style>
<style>
/* 自定义行颜色 */
.superAdmin-row {
    background-color: #ebeef5 !important;
    /* color:#fff; */
}
.anonymous-row {
    background-color: #ebeef5 !important;
    /* color:#fff; */
}
.superAdmin-row.hover-row td,
.anonymous-row.hover-row td {
    background-color: initial !important;
}
</style>
