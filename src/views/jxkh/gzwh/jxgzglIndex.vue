<template>
    <div class="app-container">
        <yo-table
            v-loading="loading"
            :data="data"
            :option="bwOption"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @search-change="searchChange"
            v-model="formParent"
            @selection-change="handleSelectionChange"
        >
            <template slot-scope="{ scope }" slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <!-- 自定义列 -->
            <template
                v-for="item in headerList"
                :slot="item.bh"
                slot-scope="scope"
            >
                <div>
                    <el-input
                        type="number"
                        :min="0"
                        v-if="scope.row.pstate != '1'"
                        style="width: 120px"
                        controls-position="right"
                        v-model="scope.row[item.bh]"
                    ></el-input>
                    <span v-else>{{ scope.row[item.bh] }}</span>
                </div>
            </template>

            <template slot-scope="{ size }" slot="menuLeft">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-lock"
                    @click.stop="lockBtn"
                    :disabled="data[0].pstate == '1'"
                >
                    {{ data[0].pstate == '1' ? '已锁定' : '锁定报表' }}
                </el-button>
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-download"
                    @click.stop="downloadFile"
                >
                    导出
                </el-button>
            </template>

            <!-- 自定义右侧操作栏 -->
            <template slot-scope="{ size }" slot="menuRight">
                <el-button
                    :disabled="data.length == 0"
                    type="success"
                    icon="el-icon-upload"
                    size="mini"
                    @click="handleSave"
                >
                    保 存
                </el-button>
            </template>

            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    v-if="row.state == '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="openModal(row, index, false)"
                >
                    修改
                </el-button>
                <el-button
                    v-if="row.state == '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-upload2"
                    @click.stop="handleImport(row, index)"
                >
                    导入
                </el-button>
                <el-button
                    v-if="row.state == '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-download"
                    @click="handleSub(row)"
                >
                    提交
                </el-button>
                <el-button
                    v-if="row.state == '1'"
                    :size="size"
                    :type="type"
                    icon="el-icon-download"
                    @click="handleExp(row)"
                >
                    下载模板
                </el-button>
                <el-button
                    v-if="row.state == '2'"
                    :size="size"
                    :type="type"
                    icon="el-icon-search"
                    @click="openModal(row, index, true)"
                >
                    查看
                </el-button>
            </template>
        </yo-table>

        <!-- <customModal ref="customModalRef"></customModal> -->
    </div>
</template>
<script>
import {
    jxgzglList,
    batchAddJxkhVo,
    lockData,
    exportData,
} from '@/api/jxkh/jxkh';
import bwOption from './infoData/newGzwhOption';
import customModal from './modal/gzglditIndex';

export default {
    dicts: ['jxkh_salary_batch_type', 'jxkh_salary_batch_state'],
    components: { customModal },
    data() {
        return {
            showContainer: true,
            loading: true,
            formParent: {},
            form: {},
            search: {},
            batchId: '',
            exportSearch: {},
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            data: [],
            bwOption: bwOption,
            multiple: false,
            rawHtml: '',
            headerList: [],
        };
    },
    created() {
        /** *添加字典项数据*/
        this.updateDictData(
            this.bwOption.column,
            'type',
            this.dict.type['jxkh_salary_batch_type']
        );
        this.updateDictData(
            this.bwOption.column,
            'state',
            this.dict.type['jxkh_salary_batch_state']
        );
    },
    methods: {
        created() {
            /** *添加字典项数据*/
            this.updateDictData(
                this.bwOption.column,
                'type',
                this.dict.type['jxkh_salary_batch_type']
            );
            this.updateDictData(
                this.bwOption.column,
                'state',
                this.dict.type['jxkh_salary_batch_state']
            );
        },
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
        },
        showDetails() {
            this.showContainer = true;
        },
        // 更新字典数据
        updateDictData(option, key, data) {
            let column = this.findObject(option, key);
            column.dicData = data;
        },
        /** 搜索按钮操作 */
        handleQuery() {
            // console.log('>>>>>>>>>>:', (bwOption.column = [{}]));
            this.page.currentPage = 1;
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 分页查询列表 */
        async getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            let response = await jxgzglList(params);
            console.log(response);
            //获取表头信息
            bwOption.column = [
                {
                    label: '绩效类型',
                    prop: 'type',
                    type: 'radio',
                    search: true,
                    slot: true,
                    dicData: [],
                    width: 80,
                    rules: [
                        {
                            required: true,
                            message: '请选择绩效类型',
                            trigger: 'change',
                        },
                    ],
                },
                {
                    label: '年度',
                    prop: 'year',
                    type: 'year',
                    search: true,
                    format: 'yyyy',
                    valueFormat: 'yyyy',
                    width: 80,
                },
                {
                    width: 80,
                    label: '季度',
                    prop: 'yearQuar',
                    showColumn: false,
                    search: true,
                    type: 'select',
                    dicData: [
                        {
                            label: '第一季度',
                            value: '1',
                        },
                        {
                            label: '第二季度',
                            value: '2',
                        },
                        {
                            label: '第三季度',
                            value: '3',
                        },
                        {
                            label: '第四季度',
                            value: '4',
                        },
                    ],
                },
                {
                    label: '月份',
                    prop: 'yearMonth',
                    type: 'month',
                    format: 'MM',
                    valueFormat: 'MM',
                    search: true,
                    width: 80,
                },
                {
                    label: '所属机构',
                    prop: 'deptName',
                    // search: true,
                },
                {
                    label: '姓名',
                    prop: 'empName',
                  
                },
            ];

            if (response.code == 0 && response.data != undefined) {
                if (response.data.list != undefined) {
                    response.data.list.map(item => {
                        if (item.status == '1') {
                            item.switch = true;
                        } else {
                            item.switch = false;
                        }
                    });
                    setTimeout(() => {
                        this.data = response.data.list;
                        this.page.total = response.data.total;
                        this.loading = false;
                    }, 1000);

                    for (let i = 0; i < response.data.list.length; i++) {
                        for (
                            let j = 0;
                            j < response.data.list[i].dynamicHeader.length;
                            j++
                        ) {
                            response.data.list[i][
                                response.data.list[i].dynamicHeader[j].bh
                            ] = response.data.list[i].dynamicHeader[j].salary;
                        }
                    }

                    // response.data.list.forEach(item => {
                    //     console.log(item.dynamicHeader);
                    //     item.dynamicHeader.forEach(element => {
                    //         item[element.bh] = element.salary;
                    //     });
                    // });

                    if (
                        response.data.list.length > 0 &&
                        response.data.list[0].dynamicHeader != undefined
                    ) {
                        let headers = response.data.list[0].dynamicHeader;

                        this.headerList = headers;

                        for (let i = 0; i < headers.length; i++) {
                            bwOption.column.push({
                                label: headers[i].subjectName,
                                prop: headers[i].bh,
                                width: 150,
                            });
                        }
                    }
                }
                this.created();
            }
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        /*下载已存在模板*/
        handleExp(row, index) {
            this.formParent = row;
            alert(row.id);
        },
        handleEdit(row, index) {
            this.formParent = row;
            this.$refs.crud.rowEdit(row, index);
        },
        handleSub(row) {
            const batchId = row.id;
            this.$modal
                .confirm('是否确认是否提交当前批次数据，提交后将不能再修改？')
                .then(() => {
                    this.handleSubs(batchId, changeState);
                })
                .catch(() => {});
        },
        async lockBtn() {
            this.loading = true;
            const res = await this.$modal
                .confirm('是否锁定当前批次数据，锁定后将不能再修改？')
                .then(() => {
                    return lockData(this.search);
                })
                .catch(() => {})
                .finally(() => {
                    this.loading = false;
                });
            if (res.code == 0) {
                this.$message.success(res.message);
                this.handleQuery();
            } else {
                this.$message.error(res.message);
            }
        },
        downloadFile() {
            const data = { userIds: this.ids, ...this.exportSearch };
            console.log('导出数据：', this.search);
            exportData(this.search).then(res => {
                console.log('导出的返回数据：', res);
                this.handleExportData(res);
            });
        },
        //批量删除
        async handleSubs(id, delFn) {
            let res = await delFn(id);
            if (res.code == 0) {
                this.getList();
                this.$message.success(res.message);
                //this.$refs.crud.toggleSelection();
            }
        },
        /** 下载模板 */
        exportExample() {
            debugger;
            if (this.search.type == null || this.search.type == '') {
                this.$modal.msgWarning('请选择下载的绩效类型！');
                return;
            } else {
                if (this.search.type == '1') {
                    //月度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearMonth == '') {
                        this.$modal.msgWarning('请选择下载的月度！');
                        return;
                    }
                }
                if (this.search.type == '2') {
                    //季度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                    if (this.search.yearQuar == '') {
                        this.$modal.msgWarning('请选择下载的季度！');
                        return;
                    }
                }
                if (this.search.type == '3') {
                    //年度
                    if (this.search.year == '') {
                        this.$modal.msgWarning('请选择下载的年度！');
                        return;
                    }
                }
                downloadExample(this.search).then(res => {
                    this.handleExportData(res);
                });
            }
        },
        /** 导出结果，处理返回的流文件 */
        handleExportData(res) {
            console.log('res:', res);
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        fileChange(event) {
            if (this.batchId == '') {
                this.$modal.msgWarning('请选择导入的批次！');
                return;
            }
            let file = document.querySelector('#fileslist').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('batchId', this.batchId);
            importGz(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport(row) {
            this.batchId = row.id;
            this.$refs.fileRef.click();
        },
        //tag样式
        tagType(row) {
            switch (row.status) {
                case '1':
                    return 'success';
                case '2':
                    return 'danger';
                case '03':
                    return 'info';
                case '04':
                    return 'warning';
                default:
                    break;
            }
        },
        openModal(row, index, isDetail) {
            this.$nextTick(() => {
                this.$refs.customModalRef.openModal(row, index, isDetail);
            });
        },
        async handleSave() {
            //数据处理，将修改后的值 保存
            this.loading = true;
            console.log(this.data);
            if (this.data.length == 0) {
                this.loading = false;
                return;
            }

            if (this.data[0].pstate == '1') {
                this.loading = false;
                this.$message.error('当前批次数据已锁定，无法进行修改！');
                return;
            }

            this.data.forEach(item => {
                item.dynamicHeader.forEach(element => {
                    element.salary = item[element.bh];
                });
            });
            console.log('列表数据：', this.data);
            const res = await batchAddJxkhVo(this.data);

            if (res.code == 0) {
                this.$message.success(res.message);
            } else {
                this.$message.error(res.message);
            }
            this.loading = false;
        },
    },
};
</script>
