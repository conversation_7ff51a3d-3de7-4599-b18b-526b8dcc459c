import validate from "@/utils/validate"

export default {
  index: true,
  indexLabel: '序号',
  selection: false,
  align: 'center',
  card: true,
  menuAlign: 'center',
  addTitle: "添加人员",
  editTitle: "修改人员",
  saveBtnText: "确定",
  editBtnText: "修改",
  updateBtnText: "确定",
  addBtn: false,
  editBtn: false,
  delBtn: false,
  searchBtn: false,
  refreshBtn: false,
  emptyBtn: false,
  searchMenuSpan: 6,
  searchMenuPosition: 'left',
  labelPosition: 'right',
  labelWidth: 150,
  tip: false,
  columnBtn: false,
  //menu:false,
  column: [
    {
      label: 'id',
      prop: 'id',
      addDisplay:false,
    },
    {
      label: '年度',
      prop: 'year',
      type: "year",
      search: true,
      format:"yyyy",
      valueFormat:"yyyy",
      addDisplay:false,
      rules: [{
        required: true,
        message: "请输入年度"
      }
      ]
    },
    {
      label: '季度',
      prop: 'yearQuar',
      showColumn: false,
      search: true,
      addDisplay:false,
      type: "select",
      dicData: [{
        label: '第一季度',
        value: '1'
      },
      {
        label: '第二季度',
        value: '2'
      },
      {
        label: '第三季度',
        value: '3'
      },
      {
        label: '第四季度',
        value: '4'
      }]
    },
    {
      label: '月份',
      prop: 'yearMonth',
      type: "month",
      search: true,
      format:"MM",
      valueFormat:"MM",
      addDisplay:false,
    },
    {
      label: '机构编码',
      prop: 'deptId',
      addDisplay:false,
    },
    {
      label: '人员编码',
      prop: 'userCode',
      search: true,
      rules: [{
        required: true,
        message: "请输入人员编码",
        trigger: "blur,change"
      }
      ]
    },
    {
      label: '姓名',
      prop: 'userName',
      search: true,
      rules: [{
        required: true,
        message: "请输入人员编码",
        trigger: "blur,change"
      }
      ]
    },
    {
      label: '身份证号',
      prop: 'idCard'
    },
    {
      label: '所在部门',
      prop: 'groupId',
      addDisplay:false,
    },
    {
      label: '性别',
      prop: 'sex'
    },
    {
      label: '民族',
      prop: 'nation'
    },
    {
      label: '出生日期',
      prop: 'birthday'
    },
    {
      label: '移动电话',
      prop: 'phone'
    },
    {
      label: '岗位',
      prop: 'post'
    },
    {
      label: '人员类别',
      prop: 'userType'
    },
    {
      label: '岗位序列',
      prop: 'orgNo',
      hide: true
    },
    {
      label: '工资单列类别',
      prop: 'wageType',
      hide: true
    },
    {
      label: '人员职级（新）',
      prop: 'userRank',
      hide: true
    },
    {
      label: '创建用户',
      prop: 'createUser',
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      addDisplay: false,
      editDisplay: false,
    }
  ]
}
