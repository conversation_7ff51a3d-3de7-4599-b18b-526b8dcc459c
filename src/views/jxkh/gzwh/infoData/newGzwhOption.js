import validate from '@/utils/validate';
export default {
    index: true,
    indexLabel: '序号',
    selection: false,
    align: 'center',
    card: true,
    menuAlign: 'center',
    emptyBtnIcon: 'el-icon-refresh',
    searchMenuSpan: 6,
    searchMenuPosition: 'left',
    addTitle: '批次模板下载',
    editTitle: '批次数据编辑',
    editBtnText: '修改',
    updateBtnText: '提交',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    searchBtn: false,
    refreshBtn: false,
    emptyBtn: false,
    labelPosition: 'right',
    menu: false,
    labelWidth: 120,
    tip: false,
    columnBtn: false,
    rowKey: 'id',
    column: [
        {
            label: '绩效类型',
            prop: 'type',
            type: 'radio',
            search: true,
            slot: true,
            dicData: [],
            width: 80,
            rules: [
                {
                    required: true,
                    message: '请选择绩效类型',
                    trigger: 'change',
                },
            ],
        },
        {
            label: '年度',
            prop: 'year',
            type: 'year',
            search: true,
            format: 'yyyy',
            valueFormat: 'yyyy',
            width: 80,
        },
        {
            width: 80,
            label: '季度',
            prop: 'yearQuar',
            showColumn: false,
            search: true,
            type: 'select',
            dicData: [
                {
                    label: '第一季度',
                    value: '1',
                },
                {
                    label: '第二季度',
                    value: '2',
                },
                {
                    label: '第三季度',
                    value: '3',
                },
                {
                    label: '第四季度',
                    value: '4',
                },
            ],
        },
        {
            label: '月份',
            prop: 'yearMonth',
            type: 'month',
            format: 'MM',
            search: true,
            width: 80,
        },
        {
            label: '所属机构',
            prop: 'deptName',
            // search: true,
        },
        {
            label: '姓名',
            prop: 'empName',
        },
    ],
};
