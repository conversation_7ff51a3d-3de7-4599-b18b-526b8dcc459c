<template>
    <div class="app-container">
        <yo-table
            v-loading="loading"
            :data="data"
            :option="tableOption"
            ref="crud"
            :page.sync="page"
            :search.sync="search"
            @on-load="onLoad"
            @search-change="searchChange"
            @row-save="rowSave"
            @row-update="rowUpdate"
            v-model="formParent"
        >
            <template slot-scope="{ scope }" slot="searchMenu">
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-search"
                    @click.stop="handleQuery"
                >
                    查询
                </el-button>
                <el-button
                    size="mini"
                    type="primary"
                    icon="el-icon-coin"
                    @click.stop="handleFq"
                >
                    发起
                </el-button>
                <el-button
                    size="mini"
                    icon="el-icon-refresh"
                    @click.stop="resetQuery()"
                >
                    重置
                </el-button>
            </template>
            <!-- 自定义左侧操作栏 -->
            <template slot-scope="{ size }" slot="menuLeft">
                <el-button
                    type="primary"
                    icon="el-icon-plus"
                    :size="size"
                    plain
                    @click.stop="handleAdd()"
                >
                    新增
                </el-button>
            </template>
            <!-- 自定义操作按钮 -->
            <template slot-scope="{ row, size, type, index }" slot="menu">
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-edit"
                    @click.stop="handleUpdate(row, index)"
                >
                    修改
                </el-button>
                <el-button
                    :size="size"
                    :type="type"
                    icon="el-icon-delete"
                    @click="handleDelete(row)"
                >
                    删除
                </el-button>
            </template>
            <!-- 自定义列 -->
            <template slot="idCard" slot-scope="scope">
                <div>
                    <span>{{ scope.row.idCard }}</span>
                    <el-button
                        v-if="scope.row.idCard"
                        type="text"
                        icon="el-icon-view"
                        @click="viewUserInfo(scope.row, 'idCard')"
                    ></el-button>
                </div>
            </template>
            <template slot="phone" slot-scope="scope">
                <div>
                    <span>{{ scope.row.phone }}</span>
                    <el-button
                        v-if="scope.row.phone"
                        type="text"
                        icon="el-icon-view"
                        @click="viewUserInfo(scope.row, 'phone')"
                    ></el-button>
                </div>
            </template>
            <template slot="userName" slot-scope="scope">
                <div>
                    <span>{{ scope.row.userName }}</span>
                    <el-button
                        v-if="scope.row.userName"
                        type="text"
                        icon="el-icon-view"
                        @click="viewUserInfo(scope.row, 'userName')"
                    ></el-button>
                </div>
            </template>
            <template slot="userCode" slot-scope="scope">
                <div>
                    <span>{{ scope.row.userCode }}</span>
                    <el-button
                        v-if="scope.row.userCode"
                        type="text"
                        icon="el-icon-view"
                        @click="viewUserInfo(scope.row, 'userCode')"
                    ></el-button>
                </div>
            </template>
            <template slot="createUser" slot-scope="scope">
                <div>
                    <span>{{ scope.row.createUser }}</span>
                    <el-button
                        v-if="scope.row.createUser"
                        type="text"
                        icon="el-icon-view"
                        @click="viewUserInfo(scope.row, 'createUser')"
                    ></el-button>
                </div>
            </template>
            <!-- 自定义右侧操作栏 -->
            <template slot-scope="{ size }" slot="menuRight">
                <input
                    id="fileslist"
                    v-show="false"
                    type="file"
                    accept=".xls"
                    ref="fileRef"
                    @change="fileChange"
                />
                <el-button
                    type="warning"
                    icon="el-icon-upload2"
                    size="mini"
                    @click="handleImport"
                >
                    导入
                </el-button>
                <el-button
                    type="primary"
                    icon="el-icon-download"
                    size="mini"
                    @click="exportExample"
                >
                    下载模板
                </el-button>
            </template>
        </yo-table>
    </div>
</template>
<script>
import {
    listConfig,
    importConfig,
    downloadExample,
    importSensitive,
    addUserImport,
    updateUserImport,
    delUserImport,
    HandleSend,
    getById,
} from '@/api/system/userimport';
import tableOption from './infoData/tableoption.js';

export default {
    name: 'Auditlog',
    dicts: [
        'yoaf_system_code',
        'yoaf_log_state',
        'yoaf_request_action',
        'yoaf_request_type',
    ],
    data() {
        return {
            loading: true,
            formParent: {},
            search: {},
            page: {
                pageSize: 10,
                currentPage: 1,
            },
            data: [],
            tableOption: tableOption,
            // 非多个禁用
            multiple: false,
            menu: false,
            yearA: '',
            yearM: '',
            yearQ: '',
        };
    },
    created() {},
    methods: {
        // 首次加载调用此方法
        onLoad(page) {
            this.getList(this.search);
        },
        /** 分页查询人员列表 */
        getList(query) {
            const params = { ...query, ...this.page };
            this.loading = true;
            listConfig(params).then(response => {
                if (response.code == 0) {
                    this.data = response.data.list;
                    this.page.total = response.data.total;
                    this.loading = false;
                }
            });
        },
        // 搜索按钮
        searchChange(params, done) {
            this.handleQuery();
            setTimeout(() => {
                done();
            }, 1000);
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.page.currentPage = 1;
            console.log(this.search);
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970' || year == 'NaN') {
                year = '';
            }
            this.search.year = year;
            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = '';
            }
            this.search.yearMonth = month;
            if (this.search.yearMonth == 'aN') {
                this.search.yearMonth = '';
            }
            this.getList(this.search);
            this.exportSearch = this.search;
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.$refs.crud.searchReset();
        },
        fileChange(event) {
            let file = document.querySelector('#fileslist').files[0];
            let formData = new FormData();
            formData.append('file', file);
            formData.append('year', this.yearA);
            formData.append('yearMonth', this.yearM);
            formData.append('yearQuar', this.yearQ);
            importConfig(formData).then(res => {
                if (res.code == 0) {
                    this.$message.success(res.message);
                    this.getList();
                }
            });
            // 清除文件，防止下次上传相同文件无反应
            event.target.value = null;
        },
        /** 导入按钮操作 */
        handleImport() {
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970') {
                year = null;
            }

            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = null;
            }
            if (date2 == 'Invalid Date') {
                month = null;
            }

            if (year == null || year == 'NaN') {
                this.$modal.alertWarning('请先选择年度!');
                return;
            }
            if (this.search.yearQuar != '' && month != null) {
                this.$modal.alertWarning('只能选择年季度或者年月');
                return;
            }
            this.yearA = year;
            this.yearM = month;
            if (this.yearM == 'aN') {
                this.yearM = '';
            }
            this.yearQ = this.search.yearQuar;
            this.$refs.fileRef.click();
        },
        /**敏感信息显隐**/
        viewUserInfo(data, type) {
            let sensitiveStatus = data[type].includes('*') ? '1' : '2';
            importSensitive(data.id, sensitiveStatus).then(response => {
                const res = response.data;
                console.log(res);
                this.data.forEach(item => {
                    if (item.id == data.id) {
                        item[type] = res[type];
                    }
                });
            });
        },
        /** 下载模板 */
        exportExample() {
            downloadExample().then(res => {
                this.handleExportData(res);
            });
        },
        /** 处理返回的流文件 */
        handleExportData(res) {
            if (!res) return;
            let data = res.data;
            let filename = res.headers['content-disposition'].split('=')[1];
            let _filename = decodeURI(filename);
            const link = document.createElement('a');
            //创建 Blob对象 可以存储二进制文件
            let blob = new Blob([data], { type: 'application/x-excel' });
            link.style.display = 'none';
            link.href = URL.createObjectURL(blob);
            link.download = _filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        /** 新增按钮操作 */
        handleAdd() {
            let uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
                /[xy]/g,
                function (c) {
                    let r = (Math.random() * 16) | 0,
                        v = c === 'x' ? r : (r & 0x3) | 0x8;
                    return v.toString(16);
                }
            );
            (this.formParent = {
                id: uuid,
            }),
                this.$refs.crud.rowAdd();
        },
        // 新增表单保存
        rowSave(form, done, loading) {
            //console.log(this.form,'我是新增.....');
            addUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);
                        this.getList();
                        done();
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        /** 修改按钮操作 */
        async handleUpdate(row, index) {
           // 因为有脱敏的数据 所以需要请求后台获取数据
            const result = await getById(row.id);
            this.formParent = result.data;
            this.$refs.crud.rowEdit(result.data, index);
        },
        // 修改表单保存
        rowUpdate(form, index, done, loading) {
            updateUserImport(form)
                .then(response => {
                    if (response.code == 0) {
                        this.$modal.msgSuccess(response.message);
                        this.getList();
                        done();
                    }
                })
                .catch(error => {
                    loading();
                });
        },
        handleDelete(row) {
            const userCode = row.userCode;
            const id = row.id;
            this.$modal
                .confirm('是否确认删除人员编号为"' + userCode + '"的数据项？')
                .then(() => {
                    this.handleDel(id, delUserImport);
                })
                .catch(() => {});
        },
        /** 删除按钮操作 */
        async handleDel(id, delFn) {
            let res = await delFn(id);
            if (res.code == 0) {
                this.getList();
                this.$message.success(res.message);
            }
        },
        /** 发起按钮操作 */
        handleFq() {
            let date = new Date(this.search.year);
            let year = date.getFullYear().toString();
            if (year == '1970' || year == 'NaN') {
                year = '';
            }
            this.search.year = year;
            let date2 = new Date(this.search.yearMonth);
            let year2 = date2.getFullYear();
            let month = ('0' + (date2.getMonth() + 1)).slice(-2);
            if (year2 == '1970' && month == '01') {
                month = '';
            }
            if (date2 == 'Invalid Date') {
                month = null;
            }
            this.search.yearMonth = month;
            if (
                this.search.yearMonth == 'aN' ||
                this.search.yearMonth == null ||
                this.search.yearMonth == 'null'
            ) {
                this.search.yearMonth = '';
            }
            if (year == null || year == 'NaN') {
                this.$modal.alertWarning('请先选择年度!');
                return;
            }
            if (this.search.yearQuar != '' && month != null && month != '') {
                this.$modal.alertWarning('只能选择年季度或者年月');
                return;
            }
            const params = { ...this.search };
            this.$modal
                .confirm(
                    '是否确认发起：' +
                        this.search.year +
                        '年-' +
                        this.search.yearMonth +
                        '月份-' +
                        this.search.yearQuar +
                        '季度的批次？'
                )
                .then(() => {
                    this.handleFq2(params, HandleSend);
                })
                .catch(() => {});
        },
        /** 发起按钮操作 */
        async handleFq2(params, delFn) {
            let res = await delFn(params);
            if (res.code == 0) {
                this.getList();
                this.$message.success(res.message);
            }
        },
    },
};
</script>
